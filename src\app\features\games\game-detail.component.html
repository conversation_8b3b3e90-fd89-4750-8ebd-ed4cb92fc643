<!-- Game Detail Page -->
<div class="min-h-screen bg-gradient-to-br from-slate-900 via-blue-950 to-gray-900">
  <!-- Header -->
  <app-header></app-header>

  <!-- Main Content -->
  <div class="pt-20 pb-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      
      <!-- Back Button -->
      <div class="mb-6">
        <button
          (click)="goBack()"
          class="flex items-center gap-2 text-gray-300 hover:text-white transition-colors"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
          Назад к каталогу
        </button>
      </div>

      <!-- Loading State -->
      <div *ngIf="loading" class="flex justify-center items-center py-20">
        <div class="bg-slate-800/60 backdrop-blur-md border border-purple-400/30 rounded-xl p-6 shadow-2xl">
          <svg class="animate-spin h-10 w-10 text-purple-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </div>
      </div>

      <!-- Error State -->
      <div *ngIf="error && !loading" class="bg-red-500/20 border border-red-500/50 rounded-lg p-6 text-center">
        <p class="text-red-300 mb-4">{{ error }}</p>
        <button 
          (click)="loadGame()" 
          class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
        >
          Попробовать снова
        </button>
      </div>

      <!-- Game Content -->
      <div *ngIf="game && !loading && !error">
        <!-- Game Header -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
          <!-- Cover Image -->
          <div class="lg:col-span-1">
            <div class="aspect-square bg-slate-800/40 border border-slate-600/50 rounded-lg overflow-hidden">
              <img 
                *ngIf="game.cover_image" 
                [src]="game.cover_image" 
                [alt]="game.title"
                class="w-full h-full object-cover">
              <div *ngIf="!game.cover_image" class="w-full h-full flex items-center justify-center text-gray-400">
                <svg class="w-24 h-24" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                </svg>
              </div>
            </div>
          </div>

          <!-- Game Info -->
          <div class="lg:col-span-2 space-y-6">
            <div>
              <h1 class="text-4xl md:text-5xl font-black text-white mb-4">{{ game.title }}</h1>
              <p *ngIf="game.subtitle" class="text-xl text-gray-300 mb-6">{{ game.subtitle }}</p>
              
              <!-- Price and Badges -->
              <div class="flex flex-wrap items-center gap-4 mb-6">
                <span class="text-3xl font-bold text-green-400">{{ formatPrice(game.price) }}</span>
                <span *ngIf="game.trial_available" class="px-3 py-1 bg-blue-600 text-white text-sm rounded-full">
                  Пробная версия доступна
                </span>
                <span *ngIf="game.requires_device" class="px-3 py-1 bg-orange-600 text-white text-sm rounded-full">
                  Требует специальное устройство
                </span>
              </div>

              <!-- Actions -->
              <div class="flex gap-4 items-center">
                <!-- Add to Cart Button (when not in library or cart) -->
                <button
                  *ngIf="!isInLibrary() && !isInCart()"
                  (click)="addToCart()"
                  class="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-lg transition-all transform hover:scale-105 text-lg font-medium shadow-lg"
                >
                  Добавить в корзину
                </button>

                <!-- Already in Cart Button -->
                <button
                  *ngIf="!isInLibrary() && isInCart()"
                  disabled
                  class="px-6 py-3 bg-gray-600 text-gray-300 rounded-lg cursor-not-allowed text-lg font-medium"
                >
                  Уже в корзине
                </button>

                <!-- Already in Library - Beautiful Text Badge -->
                <div
                  *ngIf="isInLibrary()"
                  class="flex items-center gap-3 px-4 py-2 bg-gradient-to-r from-green-500/20 to-emerald-500/20 border border-green-500/30 rounded-lg"
                >
                  <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-green-400 font-medium text-sm">Уже в вашей библиотеке</span>
                </div>
              </div>

              <!-- Creation Date -->
              <p class="text-gray-400 text-sm mt-4">
                Добавлено: {{ formatDate(game.created_at) }}
              </p>
            </div>
          </div>
        </div>

        <!-- Game Details Sections -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- Description -->
          <div class="bg-slate-800/40 border border-slate-600/50 rounded-lg p-6">
            <h2 class="text-2xl font-bold text-white mb-4">Описание</h2>
            <p class="text-gray-300 leading-relaxed">{{ game.description }}</p>
          </div>

          <!-- How to Play -->
          <div *ngIf="game.how_to_play" class="bg-slate-800/40 border border-slate-600/50 rounded-lg p-6">
            <h2 class="text-2xl font-bold text-white mb-4">Как играть</h2>
            <p class="text-gray-300 leading-relaxed">{{ game.how_to_play }}</p>
          </div>

          <!-- Target Audience -->
          <div *ngIf="game.target_audience" class="bg-slate-800/40 border border-slate-600/50 rounded-lg p-6">
            <h2 class="text-2xl font-bold text-white mb-4">Целевая аудитория</h2>
            <p class="text-gray-300 leading-relaxed">{{ game.target_audience }}</p>
          </div>

          <!-- System Requirements -->
          <div *ngIf="game.system_requirements" class="bg-slate-800/40 border border-slate-600/50 rounded-lg p-6">
            <h2 class="text-2xl font-bold text-white mb-4">Системные требования</h2>
            <p class="text-gray-300 leading-relaxed">{{ game.system_requirements }}</p>
          </div>

          <!-- Required Equipment -->
          <div *ngIf="game.required_equipment" class="bg-slate-800/40 border border-slate-600/50 rounded-lg p-6">
            <h2 class="text-2xl font-bold text-white mb-4">Необходимое оборудование</h2>
            <p class="text-gray-300 leading-relaxed">{{ game.required_equipment }}</p>
          </div>
        </div>

        <!-- Gallery Images -->
        <div *ngIf="game.gallery_images && game.gallery_images.length > 0" class="mt-8">
          <h2 class="text-2xl font-bold text-white mb-6">Галерея</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div *ngFor="let image of game.gallery_images" class="aspect-video bg-slate-800/40 border border-slate-600/50 rounded-lg overflow-hidden">
              <img [src]="image" [alt]="game.title" class="w-full h-full object-cover hover:scale-105 transition-transform duration-300">
            </div>
          </div>
        </div>

        <!-- Beautiful Conclusion Section -->
        <div class="mt-12 bg-gradient-to-br from-slate-800/60 via-blue-900/20 to-purple-900/20 border border-slate-600/30 rounded-2xl p-8 backdrop-blur-sm">
          <div class="text-center space-y-6">
            <!-- Conclusion Header -->
            <div class="space-y-3">
              <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full mb-4">
                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <h3 class="text-2xl md:text-3xl font-bold text-white">
                Готовы начать играть в {{ game.title }}?
              </h3>
              <p class="text-gray-300 text-lg max-w-2xl mx-auto leading-relaxed">
                Эта увлекательная игра станет отличным дополнением к вашему мероприятию и подарит незабываемые эмоции всем участникам.
              </p>
            </div>

            <!-- Game Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
              <!-- Price Card -->
              <div class="bg-slate-700/40 border border-slate-600/50 rounded-xl p-4">
                <div class="text-center">
                  <div class="text-2xl font-bold text-green-400 mb-1">{{ formatPrice(game.price) }}</div>
                  <div class="text-sm text-gray-400">Стоимость игры</div>
                </div>
              </div>

              <!-- Trial Card -->
              <div class="bg-slate-700/40 border border-slate-600/50 rounded-xl p-4">
                <div class="text-center">
                  <div class="text-lg font-semibold mb-1" [class]="game.trial_available ? 'text-blue-400' : 'text-gray-400'">
                    {{ game.trial_available ? 'Доступна' : 'Недоступна' }}
                  </div>
                  <div class="text-sm text-gray-400">Пробная версия</div>
                </div>
              </div>

              <!-- Device Card -->
              <div class="bg-slate-700/40 border border-slate-600/50 rounded-xl p-4">
                <div class="text-center">
                  <div class="text-lg font-semibold mb-1" [class]="game.requires_device ? 'text-orange-400' : 'text-green-400'">
                    {{ game.requires_device ? 'Требуется' : 'Не требуется' }}
                  </div>
                  <div class="text-sm text-gray-400">Спец. устройство</div>
                </div>
              </div>
            </div>

            <!-- Action Section -->
            <div class="mt-8 pt-6 border-t border-slate-600/30">
              <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <!-- Main Action Button -->
                <div *ngIf="!isInLibrary()">
                  <button
                    *ngIf="!isInCart()"
                    (click)="addToCart()"
                    class="px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-xl transition-all transform hover:scale-105 text-lg font-semibold shadow-xl hover:shadow-2xl"
                  >
                    <span class="flex items-center gap-2">
                      <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"></path>
                      </svg>
                      Добавить в корзину
                    </span>
                  </button>

                  <div
                    *ngIf="isInCart()"
                    class="px-8 py-4 bg-slate-600/50 border border-slate-500/50 text-slate-300 rounded-xl text-lg font-medium"
                  >
                    <span class="flex items-center gap-2">
                      <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                      </svg>
                      Уже в корзине
                    </span>
                  </div>
                </div>

                <!-- Library Status -->
                <div
                  *ngIf="isInLibrary()"
                  class="px-8 py-4 bg-gradient-to-r from-green-500/20 to-emerald-500/20 border border-green-500/40 rounded-xl"
                >
                  <span class="flex items-center gap-2 text-green-400 text-lg font-semibold">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Игра уже в вашей библиотеке
                  </span>
                </div>

                <!-- Back to Games Button -->
                <button
                  (click)="goBack()"
                  class="px-6 py-3 bg-slate-700/50 hover:bg-slate-600/50 border border-slate-600/50 hover:border-slate-500/50 text-gray-300 hover:text-white rounded-xl transition-all text-base font-medium"
                >
                  <span class="flex items-center gap-2">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"></path>
                    </svg>
                    Назад к играм
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
