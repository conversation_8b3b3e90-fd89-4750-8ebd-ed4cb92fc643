import { Component, OnInit, OnChanges, Input, Output, EventEmitter } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import { GameService } from '../../../../core/services/game.service';
import { Game, UpdateGameRequest } from '../../../../core/models/game.model';

@Component({
  selector: 'app-game-detail',
  standalone: false,
  templateUrl: './game-detail.component.html',
  styleUrls: ['./game-detail.component.css']
})
export class GameDetailComponent implements OnInit, OnChanges {
  @Input() gameId: number | null = null;
  @Input() isVisible = false;
  @Output() close = new EventEmitter<void>();
  @Output() gameUpdated = new EventEmitter<Game>();
  @Output() gameDeleted = new EventEmitter<number>();

  game: Game | null = null;
  loading = false;
  error = '';

  // Edit mode
  isEditing = false;
  editLoading = false;
  editError = '';
  editGame: UpdateGameRequest = {};
  selectedCoverImage: File | null = null;

  // Gallery images
  selectedGalleryFiles: File[] = [];
  galleryUploadProgress: { [key: string]: number } = {};
  galleryUploadLoading = false;

  // Delete confirmation
  showDeleteConfirm = false;
  deleteLoading = false;

  constructor(private gameService: GameService) {}

  ngOnInit(): void {
    if (this.gameId && this.isVisible) {
      this.loadGame();
    }
  }

  ngOnChanges(): void {
    if (this.gameId && this.isVisible && !this.game) {
      this.loadGame();
    }
  }

  loadGame(): void {
    if (!this.gameId) return;

    this.loading = true;
    this.error = '';

    this.gameService.getGame(this.gameId).subscribe({
      next: (game) => {
        this.game = game;
        this.initializeEditForm();
        this.loading = false;
      },
      error: (error) => {
        this.error = error.message || 'Failed to load game details';
        this.loading = false;
      }
    });
  }

  initializeEditForm(): void {
    if (!this.game) return;

    this.editGame = {
      title: this.game.title,
      subtitle: this.game.subtitle || '',
      description: this.game.description,
      how_to_play: this.game.how_to_play || '',
      target_audience: this.game.target_audience || '',
      requires_device: this.game.requires_device,
      price: this.game.price,
      trial_available: this.game.trial_available,
      system_requirements: this.game.system_requirements || '',
      required_equipment: this.game.required_equipment || ''
    };
  }

  toggleEdit(): void {
    this.isEditing = !this.isEditing;
    if (!this.isEditing) {
      this.initializeEditForm();
      this.selectedCoverImage = null;
      this.selectedGalleryFiles = [];
      this.galleryUploadProgress = {};
      this.editError = '';
    }
  }

  onCoverImageSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        this.editError = 'Пожалуйста, выберите файл изображения';
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        this.editError = 'Размер файла не должен превышать 5MB';
        return;
      }

      this.selectedCoverImage = file;
      this.editError = '';
    }
  }

  onGalleryFilesSelected(event: any): void {
    const files = Array.from(event.target.files) as File[];
    if (files.length === 0) return;

    // Validate each file
    for (const file of files) {
      // Validate file type (images and videos)
      if (!file.type.startsWith('image/') && !file.type.startsWith('video/')) {
        this.editError = 'Пожалуйста, выберите только файлы изображений или видео';
        return;
      }

      // Validate file size (max 10MB for videos, 5MB for images)
      const maxSize = file.type.startsWith('video/') ? 10 * 1024 * 1024 : 5 * 1024 * 1024;
      if (file.size > maxSize) {
        const maxSizeText = file.type.startsWith('video/') ? '10MB' : '5MB';
        this.editError = `Размер файла ${file.name} не должен превышать ${maxSizeText}`;
        return;
      }
    }

    // Add files to the selection
    this.selectedGalleryFiles = [...this.selectedGalleryFiles, ...files];
    this.editError = '';
  }

  removeGalleryFile(index: number): void {
    this.selectedGalleryFiles.splice(index, 1);
  }

  async removeExistingGalleryImage(galleryItemId: number): Promise<void> {
    try {
      await firstValueFrom(this.gameService.deleteGalleryItem(galleryItemId));
      // Reload game data to refresh gallery
      this.loadGame();
    } catch (error) {
      console.error('Error deleting gallery item:', error);
      this.editError = 'Ошибка при удалении изображения из галереи';
    }
  }

  async uploadGalleryFiles(gameId: number): Promise<void> {
    if (this.selectedGalleryFiles.length === 0) {
      return;
    }

    this.galleryUploadLoading = true;

    try {
      for (let i = 0; i < this.selectedGalleryFiles.length; i++) {
        const file = this.selectedGalleryFiles[i];
        const fileKey = `${file.name}_${i}`;

        this.galleryUploadProgress[fileKey] = 0;

        try {
          await firstValueFrom(this.gameService.addGalleryItem(gameId, file));
          this.galleryUploadProgress[fileKey] = 100;
        } catch (error) {
          console.error(`Error uploading file ${file.name}:`, error);
          throw new Error(`Ошибка загрузки файла ${file.name}`);
        }
      }
    } finally {
      this.galleryUploadLoading = false;
    }
  }

  async saveChanges(): Promise<void> {
    if (!this.game || !this.gameId) return;

    // Validate required fields
    if (!this.editGame.title?.trim()) {
      this.editError = 'Название игры обязательно';
      return;
    }

    if (!this.editGame.description?.trim()) {
      this.editError = 'Описание игры обязательно';
      return;
    }

    if (!this.editGame.price?.trim()) {
      this.editError = 'Цена игры обязательна';
      return;
    }

    // Validate price format
    const priceNum = parseFloat(this.editGame.price);
    if (isNaN(priceNum) || priceNum < 0) {
      this.editError = 'Введите корректную цену (должна быть больше или равна 0)';
      return;
    }

    this.editLoading = true;
    this.editError = '';

    // Prepare update data
    const updateData: UpdateGameRequest = {
      ...this.editGame,
      price: priceNum.toFixed(2)
    };

    // Add cover image if selected
    if (this.selectedCoverImage) {
      updateData.cover_image = this.selectedCoverImage;
    }

    this.gameService.updateGame(this.gameId, updateData).subscribe({
      next: (updatedGame) => {
        // Game updated successfully, now handle gallery upload separately
        if (this.selectedGalleryFiles.length > 0) {
          this.uploadGalleryForUpdatedGame(updatedGame);
        } else {
          // No gallery files, complete the process
          this.completeGameUpdate(updatedGame);
        }
      },
      error: (error) => {
        this.editLoading = false;

        // Handle validation errors
        if (error.error && typeof error.error === 'object') {
          const errors = error.error;
          if (errors.title) {
            this.editError = 'Название: ' + errors.title.join(', ');
          } else if (errors.description) {
            this.editError = 'Описание: ' + errors.description.join(', ');
          } else if (errors.price) {
            this.editError = 'Цена: ' + errors.price.join(', ');
          } else if (errors.cover_image) {
            this.editError = 'Изображение: ' + errors.cover_image.join(', ');
          } else if (errors.non_field_errors) {
            this.editError = errors.non_field_errors.join(', ');
          } else {
            this.editError = 'Ошибка при обновлении игры';
          }
        } else {
          this.editError = error.message || 'Ошибка при обновлении игры';
        }
      }
    });
  }

  confirmDelete(): void {
    this.showDeleteConfirm = true;
  }

  cancelDelete(): void {
    this.showDeleteConfirm = false;
  }

  deleteGame(): void {
    if (!this.gameId) return;

    this.deleteLoading = true;

    this.gameService.deleteGame(this.gameId).subscribe({
      next: () => {
        this.deleteLoading = false;
        this.showDeleteConfirm = false;
        this.gameDeleted.emit(this.gameId!);
        this.closeModal();
        alert('Игра успешно удалена!');
      },
      error: (error) => {
        this.deleteLoading = false;
        alert('Ошибка при удалении игры: ' + error.message);
      }
    });
  }

  closeModal(): void {
    this.isEditing = false;
    this.showDeleteConfirm = false;
    this.editError = '';
    this.selectedCoverImage = null;
    this.selectedGalleryFiles = [];
    this.galleryUploadProgress = {};
    this.close.emit();
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  formatPrice(price: string): string {
    const num = parseFloat(price);
    return num.toLocaleString('ru-RU', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    });
  }

  private async uploadGalleryForUpdatedGame(updatedGame: any): Promise<void> {
    try {
      // Validate gallery files before uploading
      for (const file of this.selectedGalleryFiles) {
        // Validate file type (images and videos)
        if (!file.type.startsWith('image/') && !file.type.startsWith('video/')) {
          throw new Error(`Неподдерживаемый формат файла: ${file.name}. Поддерживаются только изображения и видео.`);
        }

        // Validate file size (max 10MB for videos, 5MB for images)
        const maxSize = file.type.startsWith('video/') ? 10 * 1024 * 1024 : 5 * 1024 * 1024;
        if (file.size > maxSize) {
          const maxSizeText = file.type.startsWith('video/') ? '10MB' : '5MB';
          throw new Error(`Файл ${file.name} слишком большой. Максимальный размер: ${maxSizeText}`);
        }
      }

      // Upload gallery files
      await this.uploadGalleryFiles(updatedGame.id);

      // Success - complete the process
      this.completeGameUpdate(updatedGame);

    } catch (galleryError: any) {
      // Gallery upload failed, but game was already updated
      this.editLoading = false;
      this.editError = galleryError.message || 'Ошибка при загрузке файлов галереи';

      // Show warning that game was updated but gallery failed
      alert(`Игра обновлена успешно, но не удалось загрузить галерею: ${this.editError}`);

      // Still complete the update process (without gallery)
      this.completeGameUpdate(updatedGame);
    }
  }

  private completeGameUpdate(updatedGame: any): void {
    // Reload game data to get updated gallery
    this.loadGame();

    this.isEditing = false;
    this.editLoading = false;
    this.selectedCoverImage = null;
    this.selectedGalleryFiles = [];
    this.galleryUploadProgress = {};
    this.gameUpdated.emit(updatedGame);
    alert('Игра успешно обновлена!');
  }
}
